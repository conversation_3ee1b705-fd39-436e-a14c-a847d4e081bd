apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: argo-hub.datree.0.0.1
  annotations:
    argo-hub/version: '0.0.1'
    argo-hub/description: 'datree template'
    argo-hub/categories: 'argo'
    argo-hub/license: 'MIT'
    argo-hub/owner_name: '<PERSON>'
    argo-hub/owner_email: '<EMAIL>'
    argo-hub/owner_avatar: 'https://github.com/matthewchungcodefresh.png'
    argo-hub/owner_url: 'https://github.com/matthewchungcodefresh'
    argo-hub/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/datree/assets/icon.svg"
    argo-hub/icon-background: "#f4f4f4"
spec:
  templates:
    - name: datree-policy-check
      serviceAccountName: argo-hub.datree.0.0.1
      metadata:
        annotations:
          argo-hub-template/description: 'Invokes scan using Datree'
          argo-hub-template/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/datree/assets/icon.svg"
          argo-hub-template/icon_background: "#f4f4f4"
      retryStrategy:
        limit: "1"
        retryPolicy: "Always"
        backoff:
          duration: "5s"
      inputs:
        artifacts:
          - name: REPO
            path: /tmp/repo
        parameters:
          # required
          - name: DATREE_TOKEN
            default: datree-secret
          - name: INPUT_PATH
          # optional
          - name: CLI_ARGUMENTS
            default: ""
          - name: IS_HELM_CHART
            default: false
          - name: HELM_ARGUMENTS
            default: ""
          - name: IS_KUSTOMIZATION
            default: false
          - name: KUSTOMIZE_ARGUMENTS
            default: ""
          - name: WORKING_DIRECTORY
            default: /tmp/repo
      script:
        imagePullPolicy: Always
        image: datree/codefresh-datree
        workingDir: /tmp/repo
        env:
          - name: DATREE_TOKEN
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.DATREE_TOKEN }}'
                key: token
          - name: INPUT_PATH
            value: '{{ inputs.parameters.INPUT_PATH }}'
          - name: CLI_ARGUMENTS
            value: '{{ inputs.parameters.CLI_ARGUMENTS }}'
          - name: IS_HELM_CHART
            value: '{{ inputs.parameters.IS_HELM_CHART }}'
          - name: HELM_ARGUMENTS
            value: '{{ inputs.parameters.HELM_ARGUMENTS }}'
          - name: IS_KUSTOMIZATION
            value: '{{ inputs.parameters.IS_KUSTOMIZATION }}'
          - name: KUSTOMIZE_ARGUMENTS
            value: '{{ inputs.parameters.KUSTOMIZE_ARGUMENTS }}'
          - name: WORKING_DIRECTORY
            value: '{{ inputs.parameters.WORKING_DIRECTORY }}'
        command: [sh]
        source: |
          codefresh-datree.sh
