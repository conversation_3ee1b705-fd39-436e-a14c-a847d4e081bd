apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: argo-hub.servicenow.1.0.0
  annotations:
    argo-hub/version: '1.0.0'
    argo-hub/categories: 'utilities'
    argo-hub/description: 'Execute operations against a Service Now instance'
    argo-hub/license: 'MIT'
    argo-hub/owner_name: '<PERSON>'
    argo-hub/owner_email: '<EMAIL>'
    argo-hub/owner_url: "https://github.com/lrochette"
    argo-hub/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/servicenow/assets/icon.svg"
    argo-hub/icon_background: "#f4f4f4"

spec:
  templates:
    - name: createcr
      metadata:
        annotations:
          argo-hub-template/description: 'createcr'
          argo-hub-template/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/servicenow/assets/icon.svg"
          argo-hub-template/icon_background: "#f4f4f4"
      inputs:
        parameters:
          - name: SN_INSTANCE          # aka https://instance.service-now.com
          - name: SN_AUTH              # secret name
          - name: CR_DATA
          - name: LOGLEVEL
            value: info
      outputs:
        parameters:
          - name: CR_SYSID
            valueFrom:
              path: /tmp/CR_SYSID
          - name: CR_NUMBER
            valueFrom:
              path: /tmp/CR_NUMBER
      container:
        imagePullPolicy: Always
        image: quay.io/codefreshplugins/argo-hub-workflows-servicenow-versions-1.0.0-images-integration:main
        command: ["python3", "/snow/snow.py"]
        env:
          - name: ACTION
            value: 'createcr'
          - name: SN_INSTANCE
            value: '{{ inputs.parameters.SN_INSTANCE }}'
          - name: SN_USER
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.SN_AUTH }}'
                key: username
          - name: SN_PASSWORD
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.SN_AUTH }}'
                key: password
          - name: CR_DATA
            value: '{{ inputs.parameters.CR_DATA }}'
          - name: LOGLEVEL
            value: '{{ inputs.parameters.LOGLEVEL }}'

    - name: updatecr
      metadata:
        annotations:
          argo-hub-template/description: 'updatecr'
          argo-hub-template/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/servicenow/assets/icon.svg"
          argo-hub-template/icon_background: "#f4f4f4"
      outputs:
        parameters:
          - name: FULL_JSON
            valueFrom:
              path: /tmp/CR_UPDATE_JSON
      inputs:
        parameters:
          - name: SN_INSTANCE          # aka https://instance.service-now.com
          - name: SN_AUTH              # secret name
          - name: CR_DATA
          - name: CR_SYSID
          - name: LOGLEVEL
            value: info
      container:
        imagePullPolicy: Always
        image: quay.io/codefreshplugins/argo-hub-workflows-servicenow-versions-1.0.0-images-integration:main
        command: ["python3", "/snow/snow.py"]
        env:
          - name: ACTION
            value: 'updatecr'
          - name: SN_INSTANCE
            value: '{{ inputs.parameters.SN_INSTANCE }}'
          - name: SN_USER
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.SN_AUTH }}'
                key: username
          - name: SN_PASSWORD
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.SN_AUTH }}'
                key: password
          - name: CR_DATA
            value: '{{ inputs.parameters.CR_DATA }}'
          - name: CR_SYSID
            value: '{{ inputs.parameters.CR_SYSID }}'
          - name: LOGLEVEL
            value: '{{ inputs.parameters.LOGLEVEL }}'

    - name: closecr
      metadata:
        annotations:
          argo-hub-template/description: 'closecr'
          argo-hub-template/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/servicenow/assets/icon.svg"
          argo-hub-template/icon_background: "#f4f4f4"
      outputs:
        parameters:
          - name: FULL_JSON
            valueFrom:
              path: /tmp/CR_CLOSE_JSON
      inputs:
        parameters:
          - name: SN_INSTANCE          # aka https://instance.service-now.com
          - name: SN_AUTH              # secret name
          - name: CR_DATA
            value: '{}'
          - name: CR_SYSID
          - name: LOGLEVEL
            value: info
          - name: CR_CLOSE_CODE
            value: "successful"
          - name: CR_CLOSE_NOTES
            value: "Closed automatically by Argo-Hub ServiceNow workflow template"
      container:
        imagePullPolicy: Always
        image: quay.io/codefreshplugins/argo-hub-workflows-servicenow-versions-1.0.0-images-integration:main
        command: ["python3", "/snow/snow.py"]
        env:
          - name: ACTION
            value: 'closecr'
          - name: SN_INSTANCE
            value: '{{ inputs.parameters.SN_INSTANCE }}'
          - name: SN_USER
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.SN_AUTH }}'
                key: username
          - name: SN_PASSWORD
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.SN_AUTH }}'
                key: password
          - name: CR_DATA
            value: '{{ inputs.parameters.CR_DATA }}'
          - name: CR_SYSID
            value: '{{ inputs.parameters.CR_SYSID }}'
          - name: CR_CLOSE_CODE
            value: '{{ inputs.parameters.CR_CLOSE_CODE }}'
          - name: CR_CLOSE_NOTES
            value: '{{ inputs.parameters.CR_CLOSE_NOTES }}'
          - name: LOGLEVEL
            value: '{{ inputs.parameters.LOGLEVEL }}'
