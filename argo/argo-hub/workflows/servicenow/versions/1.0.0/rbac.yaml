apiVersion: v1
kind: ServiceAccount
metadata:
  name: argo-hub.servicenow.1.0.0
  annotations:
    codefresh-marketplace/version: '1.0.0'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: argo-hub.servicenow.1.0.0
  annotations:
    codefresh-marketplace/version: '1.0.0'
rules:
  - apiGroups:
      - ""
    resources:
      - pods
    verbs:
      - get
      - watch
      - patch
  - apiGroups:
      - ""
    resources:
      - pods/log
    verbs:
      - get
      - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: argo-hub.servicenow.1.0.0
  annotations:
    codefresh-marketplace/version: '1.0.0'
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: argo-hub.servicenow.1.0.0
subjects:
  - kind: ServiceAccount
    name: argo-hub.servicenow.1.0.0
