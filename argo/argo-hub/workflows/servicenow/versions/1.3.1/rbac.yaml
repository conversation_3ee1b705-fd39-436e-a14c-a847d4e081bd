apiVersion: v1
kind: ServiceAccount
metadata:
  name: argo-hub.servicenow.1.3.1
  annotations:
    codefresh-marketplace/version: '1.3.1'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: argo-hub.servicenow.1.3.1
  annotations:
    codefresh-marketplace/version: '1.3.1'
rules:
  - apiGroups:
      - ""
    resources:
      - pods
    verbs:
      - get
      - watch
      - patch
  - apiGroups:
      - ""
    resources:
      - pods/log
    verbs:
      - get
      - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: argo-hub.servicenow.1.3.1
  annotations:
    codefresh-marketplace/version: '1.3.1'
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: argo-hub.servicenow.1.3.1
subjects:
  - kind: ServiceAccount
    name: argo-hub.servicenow.1.3.1
