# Starting-Template

## Summary

A set of templates to manage Jira issues

## Templates


1. [create comment](https://github.com/codefresh-io/argo-hub/blob/main/workflows/jira/versions/0.0.1/docs/create-comment.md)
2. [update comment](https://github.com/codefresh-io/argo-hub/blob/main/workflows/jira/versions/0.0.1/docs/update-comment.md)
3. [create issue](https://github.com/codefresh-io/argo-hub/blob/main/workflows/jira/versions/0.0.1/docs/create-issue.md)
4. [update issue](https://github.com/codefresh-io/argo-hub/blob/main/workflows/jira/versions/0.0.1/docs/update-issue.md)
5. [update issues from JQL](https://github.com/codefresh-io/argo-hub/blob/main/workflows/jira/versions/0.0.1/docs/update-all-from-jql.md)
6. [verify issue status](https://github.com/codefresh-io/argo-hub/blob/main/workflows/jira/versions/0.0.1/docs/issue-verify-status.md)
7. [verify issue status from JQL](https://github.com/codefresh-io/argo-hub/blob/main/workflows/jira/versions/0.0.1/docs/issue-verify-status-from-jql.md)
8. [issue transition status](https://github.com/codefresh-io/argo-hub/blob/main/workflows/jira/versions/0.0.1/docs/issue-transition-status.md)
9. [issue transition status and update](https://github.com/codefresh-io/argo-hub/blob/main/workflows/jira/versions/0.0.1/docs/issue-transition-status-and-update.md)


## Security

Minimal required permissions

[Full rbac permissions list](https://github.com/codefresh-io/argo-hub/blob/main/workflows/jira/versions/0.0.1/rbac.yaml)
