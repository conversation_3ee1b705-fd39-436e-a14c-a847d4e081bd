.REST
.idea
.envrc*
# Logs
*.log
jsconfig.json
.vscode

# Runtime data
pids
*.pid
*.seed

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage/*.html

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Compiled binary addons (http://nodejs.org/api/addons.html)
build/Release

# Dependency directory
# Commenting this out is preferred by some people, see
# https://www.npmjs.org/doc/misc/npm-faq.html#should-i-check-my-node_modules-folder-into-git-
node_modules

# Users Environment Variables
.lock-wscript

.env
.coverrun
.coverdata
.nyc_output
.debug
reports
cf-dev-ctrl
.DS_Store
cf-api.sublime-project
cf-api.sublime-workspace
.coveralls.yml
coverage
.idea
*.tar.gz

/nbproject/private/

/server/version.json
/errors/docs/
/errors/docs/errors.html
.vscode

allure-results