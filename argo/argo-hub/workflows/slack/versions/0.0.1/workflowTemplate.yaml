apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: argo-hub.slack.0.0.1
  annotations:
    argo-hub/version: '0.0.1'
    argo-hub/description: 'A template that enables Slack notifications'
    argo-hub/license: 'MIT'
    argo-hub/owner_name: '<PERSON>'
    argo-hub/owner_email: '<EMAIL>'
    argo-hub/owner_avatar: 'https://avatars.githubusercontent.com/u/********?v=4'
    argo-hub/owner_url: 'https://github.com/pasha-codefresh'
    argo-hub/categories: 'messaging'
    argo-hub/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/slack/assets/icon.svg"
    argo-hub/icon_background: "#49154B"
spec:
  templates:
    - name: send-message
      serviceAccountName: argo-hub.slack.0.0.1
      metadata:
        annotations:
          argo-hub-template/description: 'Send a message to a Slack channel using a hook url'
          argo-hub-template/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/slack/assets/icon.svg"
          argo-hub-template/icon_background: "#49154B"
      retryStrategy:
        limit: "10"
        retryPolicy: "Always"
        backoff:
          duration: "5s"
      inputs:
        parameters:
          - name: MODE
            value: "simple"
          - name: SLACK_HOOK_URL
          - name: SLACK_TEXT
      container:
        name: main
        imagePullPolicy: Always
        image: quay.io/codefreshplugins/argo-hub-workflows-slack-versions-0.0.1-images-send-message:main
        command:
          - node
          - /usr/src/app/index.js
        env:
          - name: MODE
            value: '{{ inputs.parameters.MODE }}'
          - name: SLACK_HOOK_URL
            value: '{{ inputs.parameters.SLACK_HOOK_URL }}'
          - name: SLACK_TEXT
            value: '{{ inputs.parameters.SLACK_TEXT }}'
