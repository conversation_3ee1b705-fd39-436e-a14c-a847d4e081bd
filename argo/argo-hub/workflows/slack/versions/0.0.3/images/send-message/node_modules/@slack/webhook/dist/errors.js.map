{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../src/errors.ts"], "names": [], "mappings": ";;;AASA;;GAEG;AACH,IAAY,SAGX;AAHD,WAAY,SAAS;IACnB,yDAA4C,CAAA;IAC5C,mDAAsC,CAAA;AACxC,CAAC,EAHW,SAAS,GAAT,iBAAS,KAAT,iBAAS,QAGpB;AAcD;;GAEG;AACH,SAAS,aAAa,CAAC,KAAY,EAAE,IAAe;IAClD,kGAAkG;IAClG,MAAM,UAAU,GAAG,KAA4B,CAAC;IAChD,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;IACvB,OAAO,UAAwB,CAAC;AAClC,CAAC;AAED;;;GAGG;AACH,SAAgB,wBAAwB,CAAC,QAAoB;IAC3D,MAAM,KAAK,GAAG,aAAa,CACzB,IAAI,KAAK,CAAC,6BAA6B,QAAQ,CAAC,OAAO,EAAE,CAAC,EAC1D,SAAS,CAAC,YAAY,CACiB,CAAC;IAC1C,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC1B,OAAO,KAAoC,CAAC;AAC9C,CAAC;AAPD,4DAOC;AAED;;;GAGG;AACH,SAAgB,qBAAqB,CAAC,QAAkD;IACtF,MAAM,KAAK,GAAG,aAAa,CACzB,IAAI,KAAK,CAAC,iDAAiD,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EACtF,SAAS,CAAC,SAAS,CACiB,CAAC;IACvC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC1B,OAAO,KAAiC,CAAC;AAC3C,CAAC;AAPD,sDAOC"}