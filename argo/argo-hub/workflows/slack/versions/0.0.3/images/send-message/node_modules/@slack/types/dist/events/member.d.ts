export interface MemberJoinedChannelEvent {
    type: 'member_joined_channel';
    user: string;
    channel: string;
    channel_type: string;
    team: string;
    inviter?: string;
    event_ts: string;
}
export interface MemberLeftChannelEvent {
    type: 'member_left_channel';
    user: string;
    channel: string;
    channel_type: string;
    team: string;
    event_ts: string;
}
//# sourceMappingURL=member.d.ts.map