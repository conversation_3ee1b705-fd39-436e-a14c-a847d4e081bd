{"version": 3, "file": "block-elements.d.ts", "sourceRoot": "", "sources": ["../../src/block-kit/block-elements.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AAC9C,OAAO,KAAK,EACV,WAAW,EACX,MAAM,EACN,gBAAgB,EAChB,eAAe,EACf,oBAAoB,EACpB,cAAc,EACf,MAAM,uBAAuB,CAAC;AAC/B,OAAO,KAAK,EACV,UAAU,EACV,WAAW,EACX,YAAY,EACZ,SAAS,EACT,kBAAkB,EAClB,aAAa,EACb,kBAAkB,EAClB,iBAAiB,EACjB,cAAc,EACf,MAAM,cAAc,CAAC;AAEtB;;;;GAIG;AACH,MAAM,WAAW,MAAO,SAAQ,UAAU,EAAE,WAAW;IACrD;;OAEG;IACH,IAAI,EAAE,QAAQ,CAAC;IACf;;;OAGG;IACH,IAAI,EAAE,gBAAgB,CAAC;IACvB;;;OAGG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IACf;;;;OAIG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IACb;;;;;;;OAOG;IACH,KAAK,CAAC,EAAE,WAAW,CAAC;IACpB;;;OAGG;IACH,mBAAmB,CAAC,EAAE,MAAM,CAAC;CAC9B;AAED;;;;GAIG;AACH,MAAM,WAAW,UAAW,SAAQ,UAAU,EAAE,WAAW,EAAE,SAAS;IACpE;;OAEG;IACH,IAAI,EAAE,YAAY,CAAC;IACnB;;;OAGG;IACH,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC;IAC3B;;OAEG;IACH,OAAO,EAAE,MAAM,EAAE,CAAC;CACnB;AAED;;;;GAIG;AACH,MAAM,WAAW,UAAW,SAAQ,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,aAAa;IACnF;;OAEG;IACH,IAAI,EAAE,YAAY,CAAC;IACnB;;;OAGG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;CACvB;AAED;;;;;;;GAOG;AACH,MAAM,WAAW,cAAe,SAAQ,UAAU,EAAE,WAAW,EAAE,SAAS;IACxE;;OAEG;IACH,IAAI,EAAE,gBAAgB,CAAC;IACvB;;;;OAIG;IACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;CAC5B;AAED;;;;GAIG;AACH,MAAM,WAAW,UAAW,SAAQ,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,aAAa;IACpF;;OAEG;IACH,IAAI,EAAE,kBAAkB,CAAC;IACzB;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;CACxB;AAED;;;;GAIG;AACH,MAAM,WAAW,SAAU,SAAQ,UAAU;IAC3C;;OAEG;IACH,IAAI,EAAE,YAAY,CAAC;IACnB;;;;OAIG;IACH,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC;IACrB;;;OAGG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;CACpB;AAED;;;;GAIG;AACH,MAAM,MAAM,YAAY,GAAG;IACzB;;OAEG;IACH,IAAI,EAAE,OAAO,CAAC;IACd;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;CAClB,GAAG,CAAC,cAAc,GAAG,oBAAoB,CAAC,CAAC;AAO5C;;;;;;;;GAQG;AACH,MAAM,MAAM,MAAM,GAAG,WAAW,GAAG,YAAY,GAAG,mBAAmB,GAAG,cAAc,GAAG,cAAc,CAAC;AAExG;;;;;;;;;GASG;AACH,MAAM,MAAM,WAAW,GACnB,gBAAgB,GAChB,iBAAiB,GACjB,wBAAwB,GACxB,mBAAmB,GACnB,mBAAmB,CAAC;AAExB;;;;;GAKG;AACH,MAAM,WAAW,WAAY,SAAQ,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,aAAa;IACpF;;OAEG;IACH,IAAI,EAAE,cAAc,CAAC;IACrB;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;CACvB;AAED;;;;;GAKG;AACH,MAAM,WAAW,gBAAiB,SAAQ,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,kBAAkB,EAAE,aAAa;IAC7G;;OAEG;IACH,IAAI,EAAE,oBAAoB,CAAC;IAC3B;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,EAAE,CAAC;CAC1B;AAED;;;;;GAKG;AACH,MAAM,WAAW,YAAa,SAAQ,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,aAAa;IACrF;;OAEG;IACH,IAAI,EAAE,eAAe,CAAC;IAEtB;;;OAGG;IACH,cAAc,CAAC,EAAE,eAAe,CAAC;IAGjC;;;OAGG;IACH,OAAO,CAAC,EAAE,eAAe,EAAE,CAAC;IAI5B;;;OAGG;IACH,aAAa,CAAC,EAAE;QACd,KAAK,EAAE,gBAAgB,CAAC;QACxB,OAAO,EAAE,eAAe,EAAE,CAAC;KAC5B,EAAE,CAAC;CACL;AAED;;;;;GAKG;AACH,MAAM,WAAW,iBAAkB,SAAQ,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,kBAAkB,EAAE,aAAa;IAC9G;;OAEG;IACH,IAAI,EAAE,qBAAqB,CAAC;IAE5B;;;OAGG;IACH,eAAe,CAAC,EAAE,eAAe,EAAE,CAAC;IAGpC;;;OAGG;IACH,OAAO,CAAC,EAAE,eAAe,EAAE,CAAC;IAI5B;;;OAGG;IACH,aAAa,CAAC,EAAE;QACd,KAAK,EAAE,gBAAgB,CAAC;QACxB,OAAO,EAAE,eAAe,EAAE,CAAC;KAC5B,EAAE,CAAC;CACL;AAED;;;;;GAKG;AACH,MAAM,WAAW,mBAAoB,SAAQ,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,aAAa,EAAE,cAAc;IAC5G;;OAEG;IACH,IAAI,EAAE,sBAAsB,CAAC;IAC7B;;;OAGG;IACH,oBAAoB,CAAC,EAAE,MAAM,CAAC;IAC9B;;;OAGG;IACH,+BAA+B,CAAC,EAAE,OAAO,CAAC;IAC1C;;OAEG;IACH,MAAM,CAAC,EAAE;QAEP,OAAO,CAAC,EAAE,CAAC,IAAI,GAAG,MAAM,GAAG,SAAS,GAAG,QAAQ,CAAC,EAAE,CAAC;QACnD,gCAAgC,CAAC,EAAE,OAAO,CAAC;QAC3C,iBAAiB,CAAC,EAAE,OAAO,CAAC;KAC7B,CAAC;CACH;AAID;;;;;GAKG;AACH,MAAM,WAAW,wBACf,SAAQ,UAAU,EAChB,WAAW,EACX,SAAS,EACT,kBAAkB,EAClB,aAAa;IACf;;OAEG;IACH,IAAI,EAAE,4BAA4B,CAAC;IAEnC;;;OAGG;IACH,qBAAqB,CAAC,EAAE,MAAM,EAAE,CAAC;IACjC;;;OAGG;IACH,+BAA+B,CAAC,EAAE,OAAO,CAAC;IAC1C;;OAEG;IACH,MAAM,CAAC,EAAE;QAEP,OAAO,CAAC,EAAE,CAAC,IAAI,GAAG,MAAM,GAAG,SAAS,GAAG,QAAQ,CAAC,EAAE,CAAC;QACnD,gCAAgC,CAAC,EAAE,OAAO,CAAC;QAC3C,iBAAiB,CAAC,EAAE,OAAO,CAAC;KAC7B,CAAC;CACH;AAED;;;;;GAKG;AACH,MAAM,WAAW,cAAe,SAAQ,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,aAAa,EAAE,cAAc;IACvG;;OAEG;IACH,IAAI,EAAE,iBAAiB,CAAC;IACxB;;OAEG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;CAC1B;AAED;;;;;GAKG;AACH,MAAM,WAAW,mBAAoB,SAAQ,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,kBAAkB,EAAE,aAAa;IAChH;;OAEG;IACH,IAAI,EAAE,uBAAuB,CAAC;IAE9B;;OAEG;IACH,gBAAgB,CAAC,EAAE,MAAM,EAAE,CAAC;CAC7B;AAED;;;;;GAKG;AACH,MAAM,WAAW,cAAe,SAAQ,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,aAAa;IACvF;;OAEG;IACH,IAAI,EAAE,iBAAiB,CAAC;IAExB;;OAEG;IACH,cAAc,CAAC,EAAE,eAAe,CAAC;IACjC;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,MAAM,CAAC;CAC3B;AAED;;;;GAIG;AACH,MAAM,WAAW,mBAAoB,SAAQ,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,kBAAkB,EAAE,aAAa;IAChH;;OAEG;IACH,IAAI,EAAE,uBAAuB,CAAC;IAE9B;;OAEG;IACH,eAAe,CAAC,EAAE,eAAe,EAAE,CAAC;IACpC;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,MAAM,CAAC;CAC3B;AAMD;;;;;;GAMG;AACH,MAAM,WAAW,WAAY,SAAQ,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,aAAa;IACrF;;OAEG;IACH,IAAI,EAAE,cAAc,CAAC;IACrB;;OAEG;IACH,kBAAkB,EAAE,OAAO,CAAC;IAC5B;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;CACpB;AAED;;;;;;;;GAQG;AACH,MAAM,WAAW,QAAS,SAAQ,UAAU,EAAE,WAAW;IACvD;;OAEG;IACH,IAAI,EAAE,UAAU,CAAC;IAEjB;;OAEG;IACH,OAAO,EAAE,eAAe,EAAE,CAAC;CAC5B;AAED;;;;GAIG;AACH,MAAM,WAAW,cAAe,SAAQ,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,aAAa;IACxF;;OAEG;IACH,IAAI,EAAE,kBAAkB,CAAC;IACzB;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB;;;OAGG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB;;;OAGG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB;;;OAGG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;CACrB;AAED;;;;GAIG;AACH,MAAM,WAAW,YAAa,SAAQ,UAAU,EAAE,WAAW,EAAE,SAAS;IACtE;;OAEG;IACH,IAAI,EAAE,eAAe,CAAC;IACtB;;;OAGG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB;;OAEG;IACH,OAAO,EAAE,MAAM,EAAE,CAAC;CACnB;AAED;;;;;;GAMG;AACH,MAAM,WAAW,UAAW,SAAQ,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,aAAa;IACnF;;OAEG;IACH,IAAI,EAAE,YAAY,CAAC;IACnB;;;;OAIG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB;;;OAGG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED;;;;GAIG;AACH,MAAM,WAAW,QAAS,SAAQ,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,aAAa;IAClF;;OAEG;IACH,IAAI,EAAE,gBAAgB,CAAC;IACvB;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;CACxB;AAED;;;GAGG;AACH,MAAM,WAAW,cAAe,SAAQ,WAAW;IACjD;;OAEG;IACH,IAAI,EAAE,iBAAiB,CAAC;IACxB;;;OAGG;IACH,IAAI,EAAE,gBAAgB,CAAC;IACvB;;OAEG;IACH,QAAQ,EAAE;QACR;;;WAGG;QACH,OAAO,EAAE;YACP;;eAEG;YACH,GAAG,EAAE,MAAM,CAAC;YACZ;;;eAGG;YACH,6BAA6B,CAAC,EAAE;gBAC9B;;;mBAGG;gBACH,IAAI,EAAE,MAAM,CAAC;gBACb;;;mBAGG;gBACH,KAAK,EAAE,MAAM,CAAC;aACf,EAAE,CAAC;SACL,CAAC;KACH,CAAC;IACF;;;;;;;OAOG;IACH,KAAK,CAAC,EAAE,WAAW,CAAC;IACpB;;;OAGG;IACH,mBAAmB,CAAC,EAAE,MAAM,CAAC;CAC9B;AAED;;GAEG;AACH,MAAM,WAAW,wBAAyB,SAAQ,iBAAiB;IACjE;;OAEG;IACH,IAAI,EAAE,WAAW,CAAC;IAClB;;OAEG;IACH,KAAK,EAAE,MAAM,GAAG,SAAS,GAAG,UAAU,CAAC;CACxC;AAED;;GAEG;AACH,MAAM,WAAW,aAAc,SAAQ,iBAAiB;IACtD;;OAEG;IACH,IAAI,EAAE,OAAO,CAAC;IACd;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,MAAM,WAAW,sBAAuB,SAAQ,iBAAiB;IAC/D;;OAEG;IACH,IAAI,EAAE,SAAS,CAAC;IAChB;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;CACpB;AAED;;GAEG;AACH,MAAM,WAAW,YAAa,SAAQ,iBAAiB;IACrD;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAClB;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,MAAM,EAAE,MAAM,CAAC;IACf;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED;;GAEG;AACH,MAAM,WAAW,aAAc,SAAQ,iBAAiB;IACtD;;OAEG;IACH,IAAI,EAAE,OAAO,CAAC;IACd;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;CACd;AAED;;GAEG;AACH,MAAM,WAAW,YAAa,SAAQ,iBAAiB;IACrD;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB;;OAEG;IACH,GAAG,EAAE,MAAM,CAAC;CACb;AAED;;GAEG;AACH,MAAM,WAAW,mBAAoB,SAAQ,iBAAiB;IAC5D;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;CACjB;AAED;;GAEG;AACH,MAAM,WAAW,YAAa,SAAQ,iBAAiB;IACrD;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;CACd;AAED;;GAEG;AACH,MAAM,WAAW,mBAAoB,SAAQ,iBAAiB;IAC5D;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;CACjB;AAED;;GAEG;AACH,MAAM,WAAW,wBAAyB,SAAQ,iBAAiB;IACjE;;OAEG;IACH,IAAI,EAAE,WAAW,CAAC;IAClB;;OAEG;IACH,YAAY,EAAE,MAAM,CAAC;CACtB;AAED;;GAEG;AACH,MAAM,MAAM,eAAe,GACvB,wBAAwB,GACxB,aAAa,GACb,sBAAsB,GACtB,YAAY,GACZ,aAAa,GACb,YAAY,GACZ,mBAAmB,GACnB,YAAY,GACZ,mBAAmB,GACnB,wBAAwB,CAAC;AAE7B;;GAEG;AACH,MAAM,WAAW,eAAe;IAC9B;;OAEG;IACH,IAAI,EAAE,mBAAmB,CAAC;IAC1B,QAAQ,EAAE,eAAe,EAAE,CAAC;CAC7B;AAED;;GAEG;AACH,MAAM,WAAW,YAAa,SAAQ,kBAAkB;IACtD;;OAEG;IACH,IAAI,EAAE,gBAAgB,CAAC;IACvB;;OAEG;IACH,QAAQ,EAAE,eAAe,EAAE,CAAC;IAC5B;;;OAGG;IACH,KAAK,EAAE,QAAQ,GAAG,SAAS,CAAC;IAC5B;;;OAGG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB;AAED;;GAEG;AACH,MAAM,WAAW,aAAc,SAAQ,kBAAkB;IACvD;;OAEG;IACH,IAAI,EAAE,iBAAiB,CAAC;IACxB;;OAEG;IACH,QAAQ,EAAE,eAAe,EAAE,CAAC;CAC7B;AAED;;GAEG;AACH,MAAM,WAAW,oBAAqB,SAAQ,kBAAkB;IAC9D;;OAEG;IACH,IAAI,EAAE,wBAAwB,CAAC;IAC/B;;OAEG;IACH,QAAQ,EAAE,CAAC,YAAY,GAAG,YAAY,CAAC,EAAE,CAAC;CAC3C;AAED;;;;;GAKG;AACH,MAAM,WAAW,aAAc,SAAQ,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,aAAa;IACvF;;OAEG;IACH,IAAI,EAAE,iBAAiB,CAAC;IACxB;;OAEG;IACH,aAAa,CAAC,EAAE,aAAa,CAAC;CAC/B"}