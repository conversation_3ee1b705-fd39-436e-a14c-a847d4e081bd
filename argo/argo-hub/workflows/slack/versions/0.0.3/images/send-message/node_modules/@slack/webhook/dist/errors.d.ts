/// <reference types="node" />
import type { AxiosError, AxiosResponse } from 'axios';
/**
 * All errors produced by this package adhere to this interface
 */
export interface CodedError extends NodeJS.ErrnoException {
    code: ErrorCode;
}
/**
 * A dictionary of codes for errors produced by this package
 */
export declare enum ErrorCode {
    RequestError = "slack_webhook_request_error",
    HTTPError = "slack_webhook_http_error"
}
export type IncomingWebhookSendError = IncomingWebhookRequestError | IncomingWebhookHTTPError;
export interface IncomingWebhookRequestError extends CodedError {
    code: ErrorCode.RequestError;
    original: Error;
}
export interface IncomingWebhookHTTPError extends CodedError {
    code: ErrorCode.HTTPError;
    original: Error;
}
/**
 * A factory to create IncomingWebhookRequestError objects
 * @param original The original error
 */
export declare function requestErrorWithOriginal(original: AxiosError): IncomingWebhookRequestError;
/**
 * A factory to create IncomingWebhookHTTPError objects
 * @param original The original error
 */
export declare function httpErrorWithOriginal(original: AxiosError & {
    response: AxiosResponse;
}): IncomingWebhookHTTPError;
//# sourceMappingURL=errors.d.ts.map