{"version": 3, "file": "blocks.d.ts", "sourceRoot": "", "sources": ["../../src/block-kit/blocks.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EACV,MAAM,EACN,UAAU,EACV,UAAU,EACV,cAAc,EACd,UAAU,EACV,SAAS,EACT,YAAY,EACZ,WAAW,EACX,WAAW,EACX,QAAQ,EACR,cAAc,EACd,YAAY,EACZ,aAAa,EACb,YAAY,EACZ,oBAAoB,EACpB,aAAa,EACb,eAAe,EACf,MAAM,EACN,UAAU,EACV,QAAQ,EACR,cAAc,EACf,MAAM,kBAAkB,CAAC;AAC1B,OAAO,KAAK,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,uBAAuB,CAAC;AAEhH,MAAM,WAAW,KAAK;IACpB;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;;;;;OAMG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED;;;GAGG;AACH,MAAM,MAAM,UAAU,GAClB,UAAU,GACV,YAAY,GACZ,YAAY,GACZ,YAAY,GACZ,YAAY,GACZ,UAAU,GACV,SAAS,GACT,WAAW,GACX,UAAU,GACV,aAAa,CAAC;AAElB;;;GAGG;AACH,MAAM,MAAM,QAAQ,GAAG,UAAU,GAAG,KAAK,CAAC;AAE1C;;;GAGG;AACH,MAAM,MAAM,mBAAmB,GAC3B,MAAM,GACN,UAAU,GACV,UAAU,GACV,cAAc,GACd,WAAW,GACX,QAAQ,GACR,YAAY,GACZ,MAAM,GACN,UAAU,GACV,cAAc,GACd,aAAa,CAAC;AAElB;;;GAGG;AACH,MAAM,WAAW,YAAa,SAAQ,KAAK;IACzC;;OAEG;IACH,IAAI,EAAE,SAAS,CAAC;IAChB;;;OAGG;IACH,QAAQ,EAAE,mBAAmB,EAAE,CAAC;CACjC;AAED;;;GAGG;AACH,MAAM,MAAM,mBAAmB,GAAG,YAAY,GAAG,UAAU,CAAC;AAE5D;;;GAGG;AACH,MAAM,WAAW,YAAa,SAAQ,KAAK;IACzC;;OAEG;IACH,IAAI,EAAE,SAAS,CAAC;IAChB;;;OAGG;IACH,QAAQ,EAAE,mBAAmB,EAAE,CAAC;CACjC;AAED;;;;GAIG;AACH,MAAM,WAAW,YAAa,SAAQ,KAAK;IACzC;;OAEG;IACH,IAAI,EAAE,SAAS,CAAC;CACjB;AAED;;;;;;GAMG;AACH,MAAM,WAAW,SAAU,SAAQ,KAAK;IACtC;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;IACf;;OAEG;IACH,WAAW,EAAE,MAAM,CAAC;CACrB;AAED;;;;GAIG;AACH,MAAM,WAAW,WAAY,SAAQ,KAAK;IACxC;;OAEG;IACH,IAAI,EAAE,QAAQ,CAAC;IACf;;;OAGG;IACH,IAAI,EAAE,gBAAgB,CAAC;CACxB;AAED;;;GAGG;AACH,MAAM,MAAM,UAAU,GAAG;IACvB;;OAEG;IACH,IAAI,EAAE,OAAO,CAAC;IACd;;;OAGG;IACH,QAAQ,EAAE,MAAM,CAAC;IACjB;;;OAGG;IACH,KAAK,CAAC,EAAE,gBAAgB,CAAC;CAC1B,GAAG,KAAK,GACP,CAAC,cAAc,GAAG,oBAAoB,CAAC,CAAC;AAE1C;;;GAGG;AACH,MAAM,MAAM,iBAAiB,GACzB,UAAU,GACV,UAAU,GACV,cAAc,GACd,UAAU,GACV,SAAS,GACT,WAAW,GACX,WAAW,GACX,cAAc,GACd,YAAY,GACZ,aAAa,GACb,MAAM,GACN,UAAU,GACV,QAAQ,CAAC;AAEb;;;;;GAKG;AACH,MAAM,WAAW,UAAW,SAAQ,KAAK;IACvC;;OAEG;IACH,IAAI,EAAE,OAAO,CAAC;IACd;;;OAGG;IACH,KAAK,EAAE,gBAAgB,CAAC;IACxB;;;OAGG;IACH,IAAI,CAAC,EAAE,gBAAgB,CAAC;IACxB;;;OAGG;IACH,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB;;OAEG;IACH,OAAO,EAAE,iBAAiB,CAAC;IAC3B;;;OAGG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC;CAC3B;AAED;;;GAGG;AACH,MAAM,MAAM,oBAAoB,GAAG,eAAe,GAAG,YAAY,GAAG,aAAa,GAAG,oBAAoB,CAAC;AAEzG;;;;;;;;GAQG;AACH,MAAM,WAAW,aAAc,SAAQ,KAAK;IAC1C;;OAEG;IACH,IAAI,EAAE,WAAW,CAAC;IAClB,QAAQ,EAAE,oBAAoB,EAAE,CAAC;CAClC;AAED;;;GAGG;AACH,MAAM,MAAM,qBAAqB,GAC7B,MAAM,GACN,UAAU,GACV,UAAU,GACV,YAAY,GACZ,WAAW,GACX,QAAQ,GACR,YAAY,GACZ,MAAM,GACN,UAAU,GACV,cAAc,CAAC;AAInB;;;;;GAKG;AACH,MAAM,WAAW,YAAa,SAAQ,KAAK;IACzC;;OAEG;IACH,IAAI,EAAE,SAAS,CAAC;IAChB;;;;OAIG;IACH,IAAI,CAAC,EAAE,UAAU,CAAC;IAClB;;;;;OAKG;IACH,MAAM,CAAC,EAAE,UAAU,EAAE,CAAC;IACtB;;OAEG;IACH,SAAS,CAAC,EAAE,qBAAqB,CAAC;IAClC;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED;;;;;GAKG;AACH,MAAM,WAAW,UAAW,SAAQ,KAAK;IACvC;;OAEG;IACH,IAAI,EAAE,OAAO,CAAC;IACd;;;;OAIG;IACH,SAAS,EAAE,MAAM,CAAC;IAClB;;OAEG;IACH,aAAa,EAAE,MAAM,CAAC;IACtB;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,KAAK,EAAE,gBAAgB,CAAC;IACxB;;;OAGG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB;;OAEG;IACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B;;OAEG;IACH,WAAW,CAAC,EAAE,gBAAgB,CAAC;CAChC"}