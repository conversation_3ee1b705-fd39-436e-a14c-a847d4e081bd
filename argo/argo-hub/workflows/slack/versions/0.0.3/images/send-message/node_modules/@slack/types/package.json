{"name": "@slack/types", "version": "2.15.0", "description": "Shared type definitions for the Node Slack SDK", "author": "Slack Technologies, LLC", "license": "MIT", "keywords": ["slack", "typescript", "types", "api"], "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*"], "engines": {"node": ">= 12.13.0", "npm": ">= 6.12.0"}, "repository": "slackapi/node-slack-sdk", "homepage": "https://tools.slack.dev/node-slack-sdk", "publishConfig": {"access": "public"}, "bugs": {"url": "https://github.com/slackapi/node-slack-sdk/issues"}, "scripts": {"prepare": "npm run build", "build": "npm run build:clean && tsc", "build:clean": "shx rm -rf ./dist", "lint": "npx @biomejs/biome check .", "lint:fix": "npx @biomejs/biome check --write .", "test": "npm run lint && npm run build && npm run test:types", "test:types": "tsd"}, "devDependencies": {"@biomejs/biome": "^2.0.5", "shx": "^0.4.0", "tsd": "^0.32.0", "typescript": "^5.5.4"}, "tsd": {"directory": "test"}}