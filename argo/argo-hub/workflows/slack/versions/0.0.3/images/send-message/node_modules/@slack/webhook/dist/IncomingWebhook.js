"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IncomingWebhook = void 0;
const axios_1 = __importDefault(require("axios"));
const errors_1 = require("./errors");
const instrument_1 = require("./instrument");
/**
 * A client for Slack's Incoming Webhooks
 */
class IncomingWebhook {
    constructor(url, defaults = {
        timeout: 0,
    }) {
        if (url === undefined) {
            throw new Error('Incoming webhook URL is required');
        }
        this.url = url;
        this.defaults = defaults;
        this.axios = axios_1.default.create({
            baseURL: url,
            httpAgent: defaults.agent,
            httpsAgent: defaults.agent,
            maxRedirects: 0,
            proxy: false,
            timeout: defaults.timeout,
            headers: {
                'User-Agent': (0, instrument_1.getUserAgent)(),
            },
        });
        this.defaults.agent = undefined;
    }
    /**
     * Send a notification to a conversation
     * @param message - the message (a simple string, or an object describing the message)
     */
    async send(message) {
        // NOTE: no support for TLS config
        let payload = Object.assign({}, this.defaults);
        if (typeof message === 'string') {
            payload.text = message;
        }
        else {
            payload = Object.assign(payload, message);
        }
        try {
            const response = await this.axios.post(this.url, payload);
            return this.buildResult(response);
            // biome-ignore lint/suspicious/noExplicitAny: errors can be anything
        }
        catch (error) {
            // Wrap errors in this packages own error types (abstract the implementation details' types)
            if (error.response !== undefined) {
                throw (0, errors_1.httpErrorWithOriginal)(error);
            }
            if (error.request !== undefined) {
                throw (0, errors_1.requestErrorWithOriginal)(error);
            }
            throw error;
        }
    }
    /**
     * Processes an HTTP response into an IncomingWebhookResult.
     */
    buildResult(response) {
        return {
            text: response.data,
        };
    }
}
exports.IncomingWebhook = IncomingWebhook;
//# sourceMappingURL=IncomingWebhook.js.map