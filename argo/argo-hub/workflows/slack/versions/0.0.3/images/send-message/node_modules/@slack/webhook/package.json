{"name": "@slack/webhook", "version": "7.0.5", "description": "Official library for using the Slack Platform's Incoming Webhooks", "author": "Slack Technologies, LLC", "license": "MIT", "keywords": ["slack", "request", "client", "http", "api", "proxy"], "main": "dist/index.js", "types": "./dist/index.d.ts", "files": ["dist/**/*"], "engines": {"node": ">= 18", "npm": ">= 8.6.0"}, "repository": "slackapi/node-slack-sdk", "homepage": "https://tools.slack.dev/node-slack-sdk/webhook", "publishConfig": {"access": "public"}, "bugs": {"url": "https://github.com/slackapi/node-slack-sdk/issues"}, "scripts": {"prepare": "npm run build", "build": "npm run build:clean && tsc", "build:clean": "shx rm -rf ./dist ./coverage", "lint": "npx @biomejs/biome check .", "lint:fix": "npx @biomejs/biome check --write .", "mocha": "mocha --config .mocharc.json src/*.spec.ts", "test": "npm run lint && npm run test:unit", "test:unit": "npm run build && c8 npm run mocha"}, "dependencies": {"@slack/types": "^2.9.0", "@types/node": ">=18.0.0", "axios": "^1.8.3"}, "devDependencies": {"@biomejs/biome": "^1.8.3", "@types/chai": "^4.3.5", "@types/mocha": "^10.0.1", "c8": "^9.1.0", "chai": "^4.3.8", "mocha": "^10.2.0", "nock": "^13.3.3", "shx": "^0.3.2", "source-map-support": "^0.5.21", "ts-node": "^8.2.0", "typescript": "^4.1.0"}}