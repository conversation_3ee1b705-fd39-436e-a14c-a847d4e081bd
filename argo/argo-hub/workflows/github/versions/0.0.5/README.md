# Github

## Summary

A set of templates to perform operations against GitHub such as reporting a commit status check, creating a pull request, and extracting webhook variables.

## Templates

1. [commit-status](https://github.com/codefresh-io/argo-hub/blob/main/workflows/github/versions/0.0.5/docs/commit-status.md)
2. [create-pr](https://github.com/codefresh-io/argo-hub/blob/main/workflows/github/versions/0.0.5/docs/create-pr.md)
3. [create-pr-comment](https://github.com/codefresh-io/argo-hub/blob/main/workflows/github/versions/0.0.5/docs/create-pr-comment.md)
4. [create-pr-codefresh](https://github.com/codefresh-io/argo-hub/blob/main/workflows/github/versions/0.0.5/docs/create-pr-codefresh.md)
5. [extract-webhook-variables](https://github.com/codefresh-io/argo-hub/blob/main/workflows/github/versions/0.0.5/docs/extract-webhook-variables.md)


## Security

Minimal required permissions

[Full rbac permissions list](https://github.com/codefresh-io/argo-hub/blob/main/workflows/github/versions/0.0.5/rbac.yaml)
