apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: argo-hub.github.0.0.1
  annotations:
    argo-hub/version: '0.0.1'
    argo-hub/description: 'Execute operations against Github'
    argo-hub/license: 'MIT'
    argo-hub/owner_name: 'Itai Gendler'
    argo-hub/owner_email: '<EMAIL>'
    argo-hub/owner_avatar: 'https://avatars.githubusercontent.com/u/********?s=120&v=4'
    argo-hub/owner_url: 'https://github.com/itai-codefresh'
    argo-hub/categories: 'git'
    argo-hub/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/github/assets/icon.svg"
    argo-hub/icon_background: "#f4f4f4"
spec:
  templates:
    - name: commit-status
      serviceAccountName: argo-hub.github.0.0.1
      metadata:
        annotations:
          argo-hub-template/description: 'Report a commit status check'
          argo-hub-template/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/github/assets/icon.svg"
          argo-hub-template/icon_background: "#f4f4f4"
      retryStrategy:
        limit: "10"
        retryPolicy: "Always"
        backoff:
          duration: "5s"
      inputs:
        parameters:
          - name: BUILD_BASE_URL
          - name: REPO_OWNER
          - name: REPO_NAME
          - name: REVISION
          - name: STATE
          - name: CONTEXT
          - name: DESCRIPTION
          - name: GITHUB_TOKEN_SECRET
      container:
        name: main
        imagePullPolicy: Always
        image: quay.io/codefreshplugins/argo-hub-github-commit-status:0.0.1-main
        command:
          - node
          - index.js
        env:
          - name: CF_BUILD_ID
            value: '{{ workflow.name }}'
          - name: CF_BUILD_BASE_URL
            value: '{{ inputs.parameters.BUILD_BASE_URL }}'
          - name: CF_REPO_OWNER
            value: '{{ inputs.parameters.REPO_OWNER }}'
          - name: CF_REPO_NAME
            value: '{{ inputs.parameters.REPO_NAME }}'
          - name: CF_REVISION
            value: '{{ inputs.parameters.REVISION }}'
          - name: STATE
            value: '{{ inputs.parameters.STATE }}'
          - name: CONTEXT
            value: '{{ inputs.parameters.CONTEXT }}'
          - name: DESCRIPTION
            value: '{{ inputs.parameters.DESCRIPTION }}'
          - name: GITHUB_TOKEN
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.GITHUB_TOKEN_SECRET }}'
                key: token

    - name: create-pr
      serviceAccountName: argo-hub.github.0.0.1
      metadata:
        annotations:
          argo-hub-template/description: 'Create a pull request'
          argo-hub-template/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/github/assets/icon.svg"
          argo-hub-template/icon_background: "#f4f4f4"
      inputs:
        artifacts:
          - name: repo
            path: /code
        parameters:
          - name: BRANCH
          - name: MESSAGE
          - name: PR_TEMPLATE
          - name: GITHUB_TOKEN_SECRET
      script:
        workingDir: '{{ inputs.artifacts.repo.path }}'
        imagePullPolicy: Always
        image: quay.io/codefreshplugins/argo-hub-github-create-pr:0.0.1-main
        command: [sh]
        source: |
          git checkout {{inputs.parameters.BRANCH}}
          export EXISTING_PR=$(hub pr list -b main -h {{ inputs.parameters.BRANCH }} -s open -f %I)
          if [ -z $EXISTING_PR ] ; then hub pull-request --base main --head {{ inputs.parameters.BRANCH }} -m {{ inputs.parameters.MESSAGE }} --no-edit -m "$(curl {{ inputs.parameters.PR_TEMPLATE }} )" ; else echo "Pull request already exists"; fi;
        env:
          - name: GITHUB_TOKEN
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.GITHUB_TOKEN_SECRET }}'
                key: token
