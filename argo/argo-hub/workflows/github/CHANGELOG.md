# Changelog

## v0.0.3 (28.03.2022)

- Add PR comment workflow
- Add `serviceAccount` to all github workflows
- Add example for `create-pr` workflow

## v0.0.2 (8.12.2021)

### extract-webhook-variables

Parses the GitHub Event Payload (JSON) and output the most useful payload fields as artifact files.

## v0.0.1 (1.12.2021)

### commit-status

Reports a commit status check.

### create-pr

Creates a pull request.
