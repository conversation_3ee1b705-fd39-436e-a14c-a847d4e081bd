apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: argo-hub.codefresh-csdp.1.1.8
  annotations:
    argo-hub/version: '1.1.8'
    argo-hub/name: 'CSDP-metadata'
    argo-hub/description: 'Templates that execute operations against CSDP'
    argo-hub/license: 'MIT'
    argo-hub/owner_name: '<PERSON><PERSON><PERSON>'
    argo-hub/owner_email: '<EMAIL>'
    argo-hub/owner_avatar: 'https://avatars.githubusercontent.com/u/********?v=4'
    argo-hub/owner_url: 'https://github.com/vadim-kharin-codefresh'
    argo-hub/categories: 'messaging'
    argo-hub/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/codefresh-csdp/assets/csdp-icon.svg"
    argo-hub/icon_background: "#142E48"
spec:
  templates:
    - name: image-enricher-git-info
      serviceAccountName: argo-hub.codefresh-csdp.1.1.8
      metadata:
        annotations:
          argo-hub-template/description: 'Enrich images with metadata and annotation such as PR, commits, committers'
          argo-hub-template/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/codefresh-csdp/assets/icon.svg"
          argo-hub-template/icon_background: "#171A2D"
      retryStrategy:
        limit: "10"
        retryPolicy: "Always"
        backoff:
          duration: "5s"
      inputs:
        parameters:
          # required
          - name: CF_API_KEY
          - name: GIT_PROVIDER
          - name: IMAGE_NAME
          - name: BRANCH
          - name: REPO
          # optional
          - name: CF_HOST_URL
            default: 'https://g.codefresh.io'
          - name: CF_API_KEY_SECRET_KEY
            default: 'token'
          - name: GITHUB_API_PATH_PREFIX
            default: ''
          - name: GITHUB_API_HOST_URL
            default: 'https://api.github.com'
          - name: GITHUB_TOKEN_SECRET_NAME
            default: 'github'
          - name: GITHUB_TOKEN_SECRET_KEY
            default: 'token'
          - name: GITHUB_CONTEXT
            default: ''
          - name: GITLAB_HOST_URL
            default: 'https://gitlab.com'
          - name: GITLAB_TOKEN_SECRET_NAME
            default: 'gitlab'
          - name: GITLAB_TOKEN_SECRET_KEY
            default: 'token'
          - name: BITBUCKET_HOST_URL
            default: 'https://api.bitbucket.org/2.0'
          - name: BITBUCKET_SECRET_NAME
            default: 'bitbucket'
          - name: BITBUCKET_USERNAME_SECRET_KEY
            default: 'username'
          - name: BITBUCKET_PASSWORD_SECRET_KEY
            default: 'password'

      outputs:
        parameters:
          - name: exit-error
            valueFrom:
              path: /cf-outputs/exit_error

      container:
        name: main
        imagePullPolicy: Always
        image: quay.io/codefreshplugins/argo-hub-codefresh-csdp-image-enricher-git-info:1.1.8-main
        command:
          - node
          - /app/src/index.js
        env:
          - name: CF_HOST_URL
            value: '{{ inputs.parameters.CF_HOST_URL }}'
          - name: CF_API_KEY
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.CF_API_KEY }}'
                key: '{{ inputs.parameters.CF_API_KEY_SECRET_KEY }}'
          - name: IMAGE_NAME
            value: '{{ inputs.parameters.IMAGE_NAME }}'
          - name: BRANCH
            value: '{{ inputs.parameters.BRANCH }}'
          - name: REPO
            value: '{{ inputs.parameters.REPO }}'
          - name: GITHUB_API_PATH_PREFIX
            value: '{{ inputs.parameters.GITHUB_API_PATH_PREFIX }}'
          - name: GITHUB_API_HOST_URL
            value: '{{ inputs.parameters.GITHUB_API_HOST_URL }}'
          - name: GIT_PROVIDER
            value: '{{ inputs.parameters.GIT_PROVIDER }}'
          - name: GITHUB_CONTEXT
            value: '{{ inputs.parameters.GITHUB_CONTEXT }}'
          - name: GITHUB_TOKEN
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.GITHUB_TOKEN_SECRET_NAME }}'
                optional: true
                key: '{{ inputs.parameters.GITHUB_TOKEN_SECRET_KEY }}'
          - name: GITLAB_HOST_URL
            value: '{{ inputs.parameters.GITLAB_HOST_URL }}'
          - name: GITLAB_TOKEN
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.GITLAB_TOKEN_SECRET_NAME }}'
                optional: true
                key: '{{ inputs.parameters.GITLAB_TOKEN_SECRET_KEY }}'
          - name: BITBUCKET_HOST_URL
            value: '{{ inputs.parameters.BITBUCKET_HOST_URL }}'
          - name: BITBUCKET_USERNAME
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.BITBUCKET_SECRET_NAME }}'
                optional: true
                key: '{{ inputs.parameters.BITBUCKET_USERNAME_SECRET_KEY }}'
          - name: BITBUCKET_PASSWORD
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.BITBUCKET_SECRET_NAME }}'
                optional: true
                key: '{{ inputs.parameters.BITBUCKET_PASSWORD_SECRET_KEY }}'
    - name: image-enricher-jira-info
      serviceAccountName: argo-hub.codefresh-csdp.1.1.8
      metadata:
        annotations:
          argo-hub-template/description: 'Enrich images with metadata and annotation such as ticket number, title, assignee, status'
          argo-hub-template/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/codefresh-csdp/assets/icon.svg"
          argo-hub-template/icon_background: "#171A2D"
      retryStrategy:
        limit: "10"
        retryPolicy: "Always"
        backoff:
          duration: "5s"

      inputs:
        parameters:
          # required
          - name: CF_API_KEY
          - name: IMAGE_NAME
          - name: JIRA_MESSAGE
          - name: JIRA_PROJECT_PREFIX
          # optional
          - name: CF_HOST_URL
            default: 'https://g.codefresh.io'
          - name: FAIL_ON_NOT_FOUND
            default: 'false'
          - name: JIRA_CONTEXT
            default: ''
          - name: JIRA_HOST_URL
            default: ''
          - name:  JIRA_API_TOKEN_SECRET
            default: ''
          - name:  JIRA_API_TOKEN_SECRET_KEY
            default: 'token'
          - name:  JIRA_EMAIL_SECRET_KEY
            default: 'email'
          - name: CF_API_KEY_SECRET_KEY
            default: 'token'

      outputs:
        parameters:
          - name: exit-error
            valueFrom:
              path: /cf-outputs/exit_error

      container:
        name: main
        imagePullPolicy: Always
        image: quay.io/codefreshplugins/argo-hub-codefresh-csdp-image-enricher-jira-info:1.1.8-main
        command:
          - node
          - /app/src/index.js
        env:
          - name: CF_HOST_URL
            value: '{{ inputs.parameters.CF_HOST_URL }}'
          - name: FAIL_ON_NOT_FOUND
            value: '{{ inputs.parameters.FAIL_ON_NOT_FOUND }}'
          - name: JIRA_CONTEXT
            value: '{{ inputs.parameters.JIRA_CONTEXT }}'
          - name: JIRA_HOST_URL
            value: '{{ inputs.parameters.JIRA_HOST_URL }}'
          - name: JIRA_PROJECT_PREFIX
            value: '{{ inputs.parameters.JIRA_PROJECT_PREFIX }}'
          - name: IMAGE_NAME
            value: '{{ inputs.parameters.IMAGE_NAME }}'
          - name: JIRA_MESSAGE
            value: '{{ inputs.parameters.JIRA_MESSAGE }}'
          - name: CF_API_KEY
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.CF_API_KEY }}'
                key: '{{ inputs.parameters.CF_API_KEY_SECRET_KEY }}'
          - name: JIRA_API_TOKEN
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.JIRA_API_TOKEN_SECRET }}'
                key: '{{ inputs.parameters.JIRA_API_TOKEN_SECRET_KEY }}'
          - name: JIRA_EMAIL
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.JIRA_API_TOKEN_SECRET }}'
                key: '{{ inputs.parameters.JIRA_EMAIL_SECRET_KEY }}'
    - name: report-image-info
      serviceAccountName: argo-hub.codefresh-csdp.1.1.8
      metadata:
        annotations:
          argo-hub-template/description: 'Report image info to argo platform'
          argo-hub-template/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/codefresh-csdp/assets/icon.svg"
          argo-hub-template/icon_background: "#171A2D"
      retryStrategy:
        limit: "10"
        retryPolicy: "Always"
        backoff:
          duration: "5s"
      volumes:
        - name: gcr-key-file
          secret:
            items:
              - key: '{{ inputs.parameters.GCR_KEY_SECRET_KEY }}'
                path: key.json
            secretName: '{{ inputs.parameters.GCR_KEY_SECRET }}'
            optional: true
        - name: docker-config
          secret:
            items:
              - key: '{{ inputs.parameters.DOCKER_CONFIG_SECRET_KEY }}'
                path: config.json
            secretName: '{{ inputs.parameters.DOCKER_CONFIG_SECRET }}'
            optional: true

      inputs:
        parameters:
          # required
          - name: IMAGE_NAME
          - name: CF_API_KEY

          # optional
          - name: REGISTRY_INSECURE
            default: 'false'
          - name: RETRIEVE_CREDENTIALS_BY_DOMAIN
            default: 'false'
          - name: CF_HOST_URL
            default: 'https://g.codefresh.io'
          - name:  WORKFLOW_NAME
            default: ''
          - name:  WORKFLOW_URL
            default: ''
          - name: LOGS_URL
            default: ''
          - name: CF_API_KEY_SECRET_KEY
            default: 'token'
          - name: AWS_ACCESS_KEY
            default: 'cf-not-exist'
          - name: AWS_SECRET_KEY
            default: 'cf-not-exist'
          - name: AWS_REGION
            default: 'cf-not-exist'
          - name: AWS_ROLE_SECRET
            default: 'cf-not-exist'
          - name: GCR_KEY_FILE_PATH
            default: ''
          - name: DOCKER_CONFIG_FILE_PATH
            default: ''
          - name: DOCKERHUB_USERNAME
            default: 'cf-not-exist'
          - name: DOCKERHUB_PASSWORD
            default: 'cf-not-exist'
          - name: REGISTRY_USERNAME
            default: 'cf-not-exist'
          - name: REGISTRY_PASSWORD
            default: 'cf-not-exist'
          - name: REGISTRY_DOMAIN
            default: 'cf-not-exist'
          - name: REGISTRY_DOMAIN_SECRET_KEY
            default: 'domain'
          - name: USERNAME_SECRET_KEY
            default: 'username'
          - name: PASSWORD_SECRET_KEY
            default: 'password'
          - name: AWS_ACCESS_KEY_SECRET_KEY
            default: 'aws-access-key'
          - name: AWS_SECRET_KEY_SECRET_KEY
            default: 'aws-secret-key'
          - name: AWS_REGION_SECRET_KEY
            default: 'aws-region'
          - name: GCR_KEY_SECRET
            default: 'gcr-key-file'
          - name: GCR_KEY_SECRET_KEY
            default: '.keyjson'
          - name: GOOGLE_REGISTRY_HOST
            default: 'gcr.io'
          - name: GOOGLE_JSON_KEY
            default: 'cf-not-exist'
          - name: GOOGLE_KEY_SECRET_KEY
            default: 'keyjson'
          - name: DOCKER_CONFIG_SECRET
            default: 'docker-registry'
          - name: DOCKER_CONFIG_SECRET_KEY
            default: '.dockerconfigjson'
          - name: AWS_ROLE_SECRET_KEY
            default: 'role'
          - name: DOCKERFILE_CONTENT
            default: ''

      outputs:
        parameters:
          - name: image-name
            valueFrom:
              path: /cf-outputs/image_name
          - name: image-sha
            valueFrom:
              path: /cf-outputs/image_sha
          - name: exit-error
            valueFrom:
              path: /cf-outputs/exit_error

      container:
        name: main
        imagePullPolicy: Always
        image: quay.io/codefreshplugins/argo-hub-codefresh-csdp-report-image-info:1.1.8-main
        volumeMounts:
          - name: gcr-key-file
            mountPath: /gcr/
          - name: docker-config
            mountPath: /.docker/
        command:
          - node
          - /app/src/index.js
        env:
          - name: CF_API_KEY
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.CF_API_KEY }}'
                key: '{{ inputs.parameters.CF_API_KEY_SECRET_KEY }}'
          - name: CF_HOST_URL
            value: '{{ inputs.parameters.CF_HOST_URL }}'
          - name: GCR_KEY_FILE_PATH
            value: '{{ inputs.parameters.GCR_KEY_FILE_PATH }}'
          - name: GOOGLE_REGISTRY_HOST
            value: '{{ inputs.parameters.GOOGLE_REGISTRY_HOST }}'
          - name: GOOGLE_JSON_KEY
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.GOOGLE_JSON_KEY }}'
                optional: true
                key: '{{ inputs.parameters.GOOGLE_KEY_SECRET_KEY }}'
          - name: DOCKER_CONFIG_FILE_PATH
            value: '{{ inputs.parameters.DOCKER_CONFIG_FILE_PATH }}'
          - name: RETRIEVE_CREDENTIALS_BY_DOMAIN
            value: '{{ inputs.parameters.RETRIEVE_CREDENTIALS_BY_DOMAIN }}'
          - name: IMAGE_NAME
            value: '{{ inputs.parameters.IMAGE_NAME }}'
          - name: REGISTRY_INSECURE
            value: '{{ inputs.parameters.REGISTRY_INSECURE }}'
          - name: WORKFLOW_NAME
            value: '{{ inputs.parameters.WORKFLOW_NAME }}'
          - name: WORKFLOW_URL
            value: '{{ inputs.parameters.WORKFLOW_URL }}'
          - name: LOGS_URL
            value: '{{ inputs.parameters.LOGS_URL }}'
          - name: DOCKERFILE_CONTENT
            value: '{{ inputs.parameters.DOCKERFILE_CONTENT }}'
          - name: AWS_ACCESS_KEY
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.AWS_ACCESS_KEY }}'
                optional: true
                key: '{{ inputs.parameters.AWS_ACCESS_KEY_SECRET_KEY }}'
          - name: AWS_SECRET_KEY
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.AWS_SECRET_KEY }}'
                optional: true
                key: '{{ inputs.parameters.AWS_SECRET_KEY_SECRET_KEY }}'
          - name: AWS_ROLE
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.AWS_ROLE_SECRET }}'
                optional: true
                key: '{{ inputs.parameters.AWS_ROLE_SECRET_KEY }}'
          - name: AWS_REGION
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.AWS_REGION }}'
                optional: true
                key: '{{ inputs.parameters.AWS_REGION_SECRET_KEY }}'
          - name: DOCKERHUB_USERNAME
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.DOCKERHUB_USERNAME }}'
                optional: true
                key: '{{ inputs.parameters.USERNAME_SECRET_KEY }}'
          - name: DOCKERHUB_PASSWORD
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.DOCKERHUB_PASSWORD }}'
                optional: true
                key: '{{ inputs.parameters.PASSWORD_SECRET_KEY }}'
          - name: REGISTRY_USERNAME
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.REGISTRY_USERNAME }}'
                optional: true
                key: '{{ inputs.parameters.USERNAME_SECRET_KEY }}'
          - name: REGISTRY_PASSWORD
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.REGISTRY_PASSWORD }}'
                optional: true
                key: '{{ inputs.parameters.PASSWORD_SECRET_KEY }}'
          - name: REGISTRY_DOMAIN
            valueFrom:
              secretKeyRef:
                name: '{{ inputs.parameters.REGISTRY_DOMAIN }}'
                optional: true
                key: '{{ inputs.parameters.REGISTRY_DOMAIN_SECRET_KEY }}'
