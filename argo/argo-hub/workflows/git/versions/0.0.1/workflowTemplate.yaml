apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: argo-hub.git.0.0.1
  annotations:
    argo-hub/version: '0.0.1'
    argo-hub/description: 'Generic Git operations'
    argo-hub/license: 'MIT'
    argo-hub/owner_name: '<PERSON><PERSON><PERSON>'
    argo-hub/owner_email: '<EMAIL>'
    argo-hub/owner_avatar: 'https://avatars.githubusercontent.com/u/77272973?v=4'
    argo-hub/owner_url: 'https://github.com/vadimgusev-codefresh'
    argo-hub/categories: 'git'
    argo-hub/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/git/assets/icon.svg"
    argo-hub/icon_background: "#f4f4f4"
spec:
  templates:
    - name: clone-s3
      metadata:
        annotations:
          argo-hub-template/description: 'Clone a repository and push it as an artifact to s3'
          argo-hub-template/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/git/assets/icon.svg"
          argo-hub-template/icon_background: "#f4f4f4"
      inputs:
        parameters:
          - name: REPO
          - name: REVISION
            default: main
          - name: GIT_TOKEN_SECRET
          - name: KEY
            default: '{{ workflow.name }}/git-repo'
        artifacts:
          - name: repo
            path: /tmp/repo
            git:
              repo: '{{ inputs.parameters.REPO }}'
              revision: '{{ inputs.parameters.REVISION }}'
              usernameSecret:
                name: '{{ inputs.parameters.GIT_TOKEN_SECRET }}'
                key: token
      outputs:
        artifacts:
          - name: repo
            path: /tmp/repo
            s3:
              key: '{{ inputs.parameters.KEY }}'
      container:
        image: alpine
        workingDir: '{{ inputs.artifacts.repo.path }}'
        command: [sh, -c]
        args: ["ls"]
