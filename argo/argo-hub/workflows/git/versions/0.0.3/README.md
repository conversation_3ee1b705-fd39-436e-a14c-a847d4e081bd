# git

## Summary

A set of templates that execute Git operations, such as committing a change, cloning a repository, cloning a repository, and executing a push as an artifact to s3 for future use.

## Templates

1. [clone-s3](https://github.com/codefresh-io/argo-hub/blob/main/workflows/git/versions/0.0.3/docs/clone-s3.md)
2. [clone](https://github.com/codefresh-io/argo-hub/blob/main/workflows/git/versions/0.0.3/docs/clone.md)
3. [commit](https://github.com/codefresh-io/argo-hub/blob/main/workflows/git/versions/0.0.3/docs/commit.md)

## Security

Minimal required permissions

[Full rbac permissions list](https://github.com/codefresh-io/argo-hub/blob/main/workflows/git/versions/0.0.3/rbac.yaml)
