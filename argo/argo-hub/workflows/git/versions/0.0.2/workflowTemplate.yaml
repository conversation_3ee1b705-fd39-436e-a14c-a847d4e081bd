apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: argo-hub.git.0.0.2
  annotations:
    argo-hub/version: '0.0.2'
    argo-hub/description: 'Templates that enables generic Git operations'
    argo-hub/license: 'MIT'
    argo-hub/owner_name: '<PERSON><PERSON><PERSON>'
    argo-hub/owner_email: '<EMAIL>'
    argo-hub/owner_avatar: 'https://avatars.githubusercontent.com/u/********?v=4'
    argo-hub/owner_url: 'https://github.com/saffi-codefresh'
    argo-hub/categories: 'git'
    argo-hub/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/git/assets/icon.svg"
    argo-hub/icon_background: "#f4f4f4"
spec:
  templates:
    - name: clone-s3
      serviceAccountName: argo-hub.git.0.0.2
      metadata:
        annotations:
          argo-hub-template/description: 'Clone a repository and push it as an artifact to s3, the s3 repository would not store in .git/config the remote url'
          argo-hub-template/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/git/assets/icon.svg"
          argo-hub-template/icon_background: "#f4f4f4"
      inputs:
        parameters:
          - name: REPO
          - name: REVISION
            default: main
          - name: GIT_TOKEN_SECRET
          - name: KEY
            default: '{{ workflow.name }}/git-repo'
        artifacts:
          - name: repo
            path: /tmp/repo
            git:
              repo: '{{ inputs.parameters.REPO }}'
              revision: '{{ inputs.parameters.REVISION }}'
              usernameSecret:
                name: '{{ inputs.parameters.GIT_TOKEN_SECRET }}'
                key: token
      outputs:
        artifacts:
          - name: repo
            path: /tmp/repo
            s3:
              key: '{{ inputs.parameters.KEY }}'
      container:
        image: alpine
        workingDir: '{{ inputs.artifacts.repo.path }}'
        command: [sh, -c]
        args: ["ls"]

    - name: clone
      serviceAccountName: argo-hub.git.0.0.2
      metadata:
        annotations:
          argo-hub-template/description: 'Clone a git http(s) url into a specified output artifact (requires git token). Note: the repository would store in .git/config the remote url & token as remote origin url'
          argo-hub-template/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/git/assets/icon.svg"
          argo-hub-template/icon_background: "#f4f4f4"
      inputs:
        parameters:
          - name: REPO_URL
          - name: REVISION
            default: main
          - name: GIT_TOKEN_SECRET
      script:
        image: bitnami/git
        env:
          - name: TOKEN
            valueFrom:
              secretKeyRef:
                name: '{{inputs.parameters.GIT_TOKEN_SECRET}}'
                key: token
        command: [ bash ]
        source: |
          REPO_URL="{{inputs.parameters.REPO_URL}}"
          REVISION="{{inputs.parameters.REVISION}}"
          DIRPATH="{{ outputs.artifacts.repo.path }}"
          echo "inputs REPO_URL:$REPO_URL REVISION:$REVISION DIRPATH:$DIRPATH"
          mkdir -p $DIRPATH && cd $DIRPATH
          PATTERN="https://" && CHANGE="https://token:$TOKEN@" && TOKEN_REPO="${REPO_URL/$PATTERN/"$CHANGE"}"
          git clone $TOKEN_REPO $DIRPATH -b $REVISION
      outputs:
        artifacts:
          - name: repo
            path: /tmp/repo

    - name: commit
      serviceAccountName: argo-hub.git.0.0.2
      metadata:
        annotations:
          argo-hub-template/description: 'Commit (& push) a git (input) artifact repository, Requires the use of : git/clone template which stores the remote origin url in .git/config, see: "git config --get remote.origin.url" '
          argo-hub-template/icon_url: "https://cdn.jsdelivr.net/gh/codefresh-io/argo-hub@main/workflows/git/assets/icon.svg"
          argo-hub-template/icon_background: "#f4f4f4"
      inputs:
        parameters:
          - name: MESSAGE
          - name: GIT_USER_NAME
          - name: GIT_USER_EMAIL
          - name: GIT_FILES
            default: '.'
        artifacts:
          - name: repo
            path: /tmp/repo
      script:
        image: bitnami/git
        command: [ bash ]
        source: |
          MESSAGE="{{inputs.parameters.MESSAGE}}"
          GIT_USER_NAME="{{inputs.parameters.GIT_USER_NAME}}"
          GIT_USER_EMAIL="{{inputs.parameters.GIT_USER_EMAIL}}"
          GIT_FILES="{{inputs.parameters.GIT_FILES}}"
          DIRPATH="{{ inputs.artifacts.repo.path }}"  && cd $DIRPATH
          echo "Using User details/message:{ GIT_USER_NAME:$GIT_USER_NAME, GIT_USER_EMAIL:$GIT_USER_EMAIL, MESSAGE:$MESSAGE }"
          # config user/email
          git config user.name "${GIT_USER_NAME}" && git config --global user.email "${GIT_USER_EMAIL}"
          # add commit & push
          echo "Using remote.origin.url as remote target"
          git add $GIT_FILES  && git commit -am "$MESSAGE" && git push

