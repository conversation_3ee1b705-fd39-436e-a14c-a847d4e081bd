{"name": "codefresh-port-entity", "version": "1.0.0", "main": "lib/index.js", "private": true, "license": "MIT", "scripts": {"build": "tsc", "format": "prettier --write '**/*.ts'", "format-check": "prettier --check '**/*.ts'", "lint": "eslint src/**/*.ts", "build-image": "docker build -t codefresh-port-entity:latest ."}, "dependencies": {"axios": "^1.2.1"}, "devDependencies": {"@trivago/prettier-plugin-sort-imports": "^4.0.0", "@tsconfig/node16": "^1.0.3", "@types/node": "^18.11.13", "eslint": "^8.29.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-github": "^4.6.0", "eslint-plugin-jest": "^27.1.6", "eslint-plugin-prettier": "^4.2.1", "prettier": "^2.8.1", "ts-node": "^10.9.1", "typescript": "^4.9.4"}}