# ArgoCD

## Summary

Multiple templates that enable operations that can be used to execute against ArgoCD applications.

## Templates

1. [action-abort](https://github.com/codefresh-io/argo-hub/blob/main/workflows/argocd/versions/0.0.1/docs/action-abort.md)

1. [action-promote-full](https://github.com/codefresh-io/argo-hub/blob/main/workflows/argocd/versions/0.0.1/docs/action-promote-full.md)

1. [action-restart](https://github.com/codefresh-io/argo-hub/blob/main/workflows/argocd/versions/0.0.1/docs/action-restart.md)

1. [action-resume](https://github.com/codefresh-io/argo-hub/blob/main/workflows/argocd/versions/0.0.1/docs/action-resume.md)

1. [action-retry](https://github.com/codefresh-io/argo-hub/blob/main/workflows/argocd/versions/0.0.1/docs/action-retry.md)

1. [history](https://github.com/codefresh-io/argo-hub/blob/main/workflows/argocd/versions/0.0.1/docs/history.md)

1. [list](https://github.com/codefresh-io/argo-hub/blob/main/workflows/argocd/versions/0.0.1/docs/list.md)

1. [rollback](https://github.com/codefresh-io/argo-hub/blob/main/workflows/argocd/versions/0.0.1/docs/rollback.md)

1. [sync](https://github.com/codefresh-io/argo-hub/blob/main/workflows/argocd/versions/0.0.1/docs/sync.md)

1. [wait](https://github.com/codefresh-io/argo-hub/blob/main/workflows/argocd/versions/0.0.1/docs/wait.md)

## Security

Minimal required permissions

[Full rbac permissions list](https://github.com/codefresh-io/argo-hub/blob/main/workflows/argocd/versions/0.0.1/rbac.yaml)
