# Changelog

## v0.0.1 (10.12.2021)

Initial version. Added the commands below

### sync

Sync an application in ArgoCD

### wait

Wait for an application to get into a desired state in ArgoCD

### rollback

Rollback an application to a given ID in ArgoCD

### list

List applications in ArgoCD and store the results as an output

### history

Show an application's history in ArgoCD and store the results as an output

### action-abort

Run the abort action against an application (rollout) in ArgoCD

### action-promote-full

Run the promote-full action against an application (rollout) in ArgoCD

### action-restart

Run the restart action against an application in ArgoCD

### action-resume

Run the resume action against an application (rollout) in ArgoCD

### action-retry

Run the retry action against an application (rollout) in ArgoCD