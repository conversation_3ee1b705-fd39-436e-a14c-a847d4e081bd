# DO NOT REMOVE the following attributes:
# annotations.codefresh.io/workflow-origin (identifies type of Workflow Template as Promotion Workflow)
# annotations.version (identifies version of Promotion Workflow used)
# annotations.description (identifies intended use of the Promotion Workflow)

# List of parameters available for Release OnEnd Hook. See full list of parameters in the documentation: 
# https://codefresh.io/docs/gitops/promotions/promotion-hooks/#default-arguments-in-promotion-workflows-with-hooks

apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: slack-release-end
  annotations:
    codefresh.io/workflow-origin: promotion
    version: 0.0.1
    description: "Edits the original release Slack message to show completion status"
spec:
  arguments:
    parameters:
      - name: RELEASE_URL
      - name: PRODUCT_NAME
      - name: COMMIT_SHA
      - name: PROMOTION_FLOW_NAME
      - name: RELEASE_ID
      # Promotion context parameters
      - name: SLACK_CHANNEL_ID
      - name: RELEASE_MESSAGE_TS
  serviceAccountName: cf-default-promotion-workflows-sa
  entrypoint: update-slack-message
  templates:
    - name: update-slack-message
      retryStrategy:
        limit: "3"
        retryPolicy: "Always"
        backoff:
          duration: "5s"
      templateRef:
        name: argo-hub.slack.0.0.3
        template: post-to-channel
      arguments:
        parameters:
          - name: SLACK_CHANNEL
            value: "{{workflow.parameters.SLACK_CHANNEL_ID}}"
          - name: SLACK_TOKEN
            value: "slack-token"  # Replace with your actual secret name
          - name: SLACK_MESSAGE_TS
            value: "{{workflow.parameters.RELEASE_MESSAGE_TS}}"
          - name: SLACK_MESSAGE
            value: |
              ✅ **Release Completed Successfully**
              
              **Product:** {{workflow.parameters.PRODUCT_NAME}}
              **Release ID:** {{workflow.parameters.RELEASE_ID}}
              **Promotion Flow:** {{workflow.parameters.PROMOTION_FLOW_NAME}}
              **Commit:** `{{workflow.parameters.COMMIT_SHA}}`
              **Release URL:** {{workflow.parameters.RELEASE_URL}}
              
              Status: ✅ Completed
          - name: SLACK_TEMPLATE_BODY
            value: |
              {
                "color": "good",
                "title": "✅ Release Completed Successfully",
                "title_link": "{{workflow.parameters.RELEASE_URL}}"
              }
          - name: SLACK_TEMPLATE_FIELDS
            value: |
              [
                {"title": "Product", "value": "{{workflow.parameters.PRODUCT_NAME}}", "short": true},
                {"title": "Release ID", "value": "{{workflow.parameters.RELEASE_ID}}", "short": true},
                {"title": "Promotion Flow", "value": "{{workflow.parameters.PROMOTION_FLOW_NAME}}", "short": true},
                {"title": "Commit", "value": "`{{workflow.parameters.COMMIT_SHA}}`", "short": true},
                {"title": "Status", "value": "✅ Completed", "short": false}
              ]
