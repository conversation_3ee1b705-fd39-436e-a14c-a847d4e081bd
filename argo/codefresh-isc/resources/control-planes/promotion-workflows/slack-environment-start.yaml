# DO NOT REMOVE the following attributes:
# annotations.codefresh.io/workflow-origin (identifies type of Workflow Template as Promotion Workflow)
# annotations.version (identifies version of Promotion Workflow used)
# annotations.description (identifies intended use of the Promotion Workflow)

# List of parameters available for Environment OnStart Hook. See full list of parameters in the documentation: 
# https://codefresh.io/docs/gitops/promotions/promotion-hooks/#default-arguments-in-promotion-workflows-with-hooks

apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: slack-environment-start
  annotations:
    codefresh.io/workflow-origin: promotion
    version: 0.0.1
    description: "Creates reply in Slack thread for environment start and enriches promotion context with environment message timestamp"
spec:
  arguments:
    parameters:
      - name: RELEASE_URL
      - name: PRODUCT_NAME
      - name: COMMIT_SHA
      - name: PROMOTION_FLOW_NAME
      - name: RELEASE_ID
      - name: ENVIRONMENT_NAME
      # Promotion context parameters from release start
      - name: SLACK_THREAD_TS
      - name: SLACK_CHANNEL_ID
      - name: RELEASE_MESSAGE_TS
  serviceAccountName: cf-default-promotion-workflows-sa
  entrypoint: main-dag
  templates:
    - name: main-dag
      dag:
        tasks:
          - name: create-environment-reply
            template: create-environment-reply
          - name: enrich-promotion-context
            templateRef:
              name: set-promotion-context
              template: generate-context
            depends: "create-environment-reply.Succeeded"
            arguments:
              parameters:
                - name: context-params
                  value: |
                    {
                      "SLACK_THREAD_TS": "{{workflow.parameters.SLACK_THREAD_TS}}",
                      "SLACK_CHANNEL_ID": "{{workflow.parameters.SLACK_CHANNEL_ID}}",
                      "RELEASE_MESSAGE_TS": "{{workflow.parameters.RELEASE_MESSAGE_TS}}",
                      "{{workflow.parameters.ENVIRONMENT_NAME}}_MESSAGE_TS": "{{tasks.create-environment-reply.outputs.parameters.message_ts}}"
                    }

    - name: create-environment-reply
      retryStrategy:
        limit: "3"
        retryPolicy: "Always"
        backoff:
          duration: "5s"
      outputs:
        parameters:
          - name: message_ts
            valueFrom:
              path: /tmp/message_ts.txt
          - name: channel_id
            valueFrom:
              path: /tmp/channel_id.txt
      templateRef:
        name: argo-hub.slack.0.0.3
        template: post-to-channel
      arguments:
        parameters:
          - name: SLACK_CHANNEL
            value: "{{workflow.parameters.SLACK_CHANNEL_ID}}"
          - name: SLACK_TOKEN
            value: "slack-token"  # Replace with your actual secret name
          - name: SLACK_THREAD_TS
            value: "{{workflow.parameters.SLACK_THREAD_TS}}"
          - name: SLACK_MESSAGE
            value: |
              🔄 **Environment Deployment Started**
              
              **Environment:** {{workflow.parameters.ENVIRONMENT_NAME}}
              **Product:** {{workflow.parameters.PRODUCT_NAME}}
              **Release ID:** {{workflow.parameters.RELEASE_ID}}
              **Commit:** `{{workflow.parameters.COMMIT_SHA}}`
              
              Status: 🟡 In Progress
          - name: SLACK_TEMPLATE_BODY
            value: |
              {
                "color": "warning",
                "title": "🔄 Environment Deployment Started"
              }
          - name: SLACK_TEMPLATE_FIELDS
            value: |
              [
                {"title": "Environment", "value": "{{workflow.parameters.ENVIRONMENT_NAME}}", "short": true},
                {"title": "Product", "value": "{{workflow.parameters.PRODUCT_NAME}}", "short": true},
                {"title": "Release ID", "value": "{{workflow.parameters.RELEASE_ID}}", "short": true},
                {"title": "Commit", "value": "`{{workflow.parameters.COMMIT_SHA}}`", "short": true},
                {"title": "Status", "value": "🟡 In Progress", "short": false}
              ]
