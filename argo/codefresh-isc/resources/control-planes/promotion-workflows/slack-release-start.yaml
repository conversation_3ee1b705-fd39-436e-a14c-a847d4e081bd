# DO NOT REMOVE the following attributes:
# annotations.codefresh.io/workflow-origin (identifies type of Workflow Template as Promotion Workflow)
# annotations.version (identifies version of Promotion Workflow used)
# annotations.description (identifies intended use of the Promotion Workflow)

# List of parameters available for Release OnStart Hook. See full list of parameters in the documentation: 
# https://codefresh.io/docs/gitops/promotions/promotion-hooks/#default-arguments-in-promotion-workflows-with-hooks

apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: slack-release-start
  annotations:
    codefresh.io/workflow-origin: promotion
    version: 0.0.1
    description: "Creates Slack thread for release start and sets promotion context with thread timestamp"
spec:
  arguments:
    parameters:
      - name: RELEASE_URL
      - name: PRODUCT_NAME
      - name: COMMIT_SHA
      - name: PROMOTION_FLOW_NAME
      - name: RELEASE_ID
  serviceAccountName: cf-default-promotion-workflows-sa
  entrypoint: main-dag
  templates:
    - name: main-dag
      dag:
        tasks:
          - name: create-slack-thread
            template: create-slack-thread
          - name: set-promotion-context
            templateRef:
              name: set-promotion-context
              template: generate-context
            depends: "create-slack-thread.Succeeded"
            arguments:
              parameters:
                - name: context-params
                  value: |
                    {
                      "SLACK_THREAD_TS": "{{tasks.create-slack-thread.outputs.parameters.message_ts}}",
                      "SLACK_CHANNEL_ID": "{{tasks.create-slack-thread.outputs.parameters.channel_id}}",
                      "RELEASE_MESSAGE_TS": "{{tasks.create-slack-thread.outputs.parameters.message_ts}}"
                    }

    - name: create-slack-thread
      retryStrategy:
        limit: "3"
        retryPolicy: "Always"
        backoff:
          duration: "5s"
      outputs:
        parameters:
          - name: message_ts
            valueFrom:
              path: /tmp/message_ts.txt
          - name: channel_id
            valueFrom:
              path: /tmp/channel_id.txt
      templateRef:
        name: argo-hub.slack.0.0.3
        template: post-to-channel
      arguments:
        parameters:
          - name: SLACK_CHANNEL
            value: "deployments"  # Replace with your actual Slack channel
          - name: SLACK_TOKEN
            value: "slack-token"  # Replace with your actual secret name
          - name: SLACK_MESSAGE
            value: |
              🚀 **Release Started**
              
              **Product:** {{workflow.parameters.PRODUCT_NAME}}
              **Release ID:** {{workflow.parameters.RELEASE_ID}}
              **Promotion Flow:** {{workflow.parameters.PROMOTION_FLOW_NAME}}
              **Commit:** `{{workflow.parameters.COMMIT_SHA}}`
              **Release URL:** {{workflow.parameters.RELEASE_URL}}
              
              Status: 🟡 In Progress
          - name: SLACK_TEMPLATE_BODY
            value: |
              {
                "color": "warning",
                "title": "🚀 Release Started",
                "title_link": "{{workflow.parameters.RELEASE_URL}}"
              }
          - name: SLACK_TEMPLATE_FIELDS
            value: |
              [
                {"title": "Product", "value": "{{workflow.parameters.PRODUCT_NAME}}", "short": true},
                {"title": "Release ID", "value": "{{workflow.parameters.RELEASE_ID}}", "short": true},
                {"title": "Promotion Flow", "value": "{{workflow.parameters.PROMOTION_FLOW_NAME}}", "short": true},
                {"title": "Commit", "value": "`{{workflow.parameters.COMMIT_SHA}}`", "short": true},
                {"title": "Status", "value": "🟡 In Progress", "short": false}
              ]
