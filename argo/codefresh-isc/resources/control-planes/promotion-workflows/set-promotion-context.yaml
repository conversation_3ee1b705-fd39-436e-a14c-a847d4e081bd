# DO NOT REMOVE the following attributes:
# annotations.codefresh.io/workflow-origin (identifies type of Workflow Template as Promotion Workflow)
# annotations.version (identifies version of Promotion Workflow used)
# annotations.description (identifies intended use of the Promotion Workflow)

# Documentation:
# This WorkflowTemplate is a helper template designed to generate and store a promotion context.
# It should be used by other workflow templates to store values in the promotion-context, which can later
# be accessed by hooks and pre-post action promotion workflows.
# 
# Example usage:
# Refer to the Codefresh documentation for examples of how to use the `set-promotion-context` template:
# https://codefresh.io/docs/gitops/promotions/promotion-context-promotion-workflows/
#
# Key Features:
# - Accepts a JSON string of key-value pairs as input (`context-params`).
# - Stores the JSON string in a file (`/tmp/promotion-context.txt`) for later use.
# - Workflow Templates triggered by a Promotion Hook can access the stored key value pairs as parameters that can be accessed globally.
#
# Notes:
# - Ensure that the `context-params` input parameter is provided in the correct JSON format.
# - This template is intended to be used as part of a larger promotion workflow.
# Example: '{"SLACK_THREAD_TS": "**********.123456", "SLACK_CHANNEL_ID": "C**********"}'

apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: set-promotion-context
  annotations:
    version: 0.0.1
    codefresh.io/workflow-origin: promotion
    description: Helper template to generate and store a promotion context with custom parameters for sharing between hooks
spec:
  entrypoint: generate-context
  templates:
    - name: generate-context
      serviceAccountName: cf-default-promotion-workflows-sa
      inputs:
        parameters:
          - name: context-params
            description: JSON string of key-value pairs
            # Example: '{"SLACK_THREAD_TS": "**********.123456", "SLACK_CHANNEL_ID": "C**********", "RELEASE_MESSAGE_TS": "**********.123456"}'
      script:
        image: alpine
        command: [sh]
        source: |
          echo '{{inputs.parameters.context-params}}' > /tmp/context.json
          CONTEXT_JSON=$(cat /tmp/context.json)

          # Validate JSON format
          if ! echo "$CONTEXT_JSON" | grep -q '^{.*}$'; then
            echo "Error: context-params must be a valid JSON object"
            exit 1
          fi

          # Output the context as is
          echo "$CONTEXT_JSON" > /tmp/promotion-context.txt
          echo "Promotion context set successfully:"
          cat /tmp/promotion-context.txt
      outputs:
        parameters:
          - name: PROMOTION_CONTEXT
            globalName: PROMOTION_CONTEXT
            valueFrom:
              path: /tmp/promotion-context.txt
