# DO NOT REMOVE the following attributes:
# annotations.codefresh.io/workflow-origin (identifies type of Workflow Template as Promotion Workflow)
# annotations.version (identifies version of Promotion Workflow used)
# annotations.description (identifies intended use of the Promotion Workflow)

# List of parameters available for Environment OnFailure Hook. See full list of parameters in the documentation: 
# https://codefresh.io/docs/gitops/promotions/promotion-hooks/#default-arguments-in-promotion-workflows-with-hooks

apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: slack-environment-failure
  annotations:
    codefresh.io/workflow-origin: promotion
    version: 0.0.1
    description: "Edits the environment reply Slack message to show failure status"
spec:
  arguments:
    parameters:
      - name: RELEASE_URL
      - name: PRODUCT_NAME
      - name: COMMIT_SHA
      - name: PROMOTION_FLOW_NAME
      - name: RELEASE_ID
      - name: ENVIRONMENT_NAME
      - name: ERROR_MESSAGE
      # Promotion context parameters
      - name: SLACK_CHANNEL_ID
      # Environment-specific message timestamp from promotion context
      # This should be passed dynamically based on environment name
      # For example: dev_MESSAGE_TS, staging_MESSAGE_TS, prod_MESSAGE_TS
      - name: ENVIRONMENT_MESSAGE_TS
  serviceAccountName: cf-default-promotion-workflows-sa
  entrypoint: update-slack-message
  templates:
    - name: update-slack-message
      retryStrategy:
        limit: "3"
        retryPolicy: "Always"
        backoff:
          duration: "5s"
      templateRef:
        name: argo-hub.slack.0.0.3
        template: post-to-channel
      arguments:
        parameters:
          - name: SLACK_CHANNEL
            value: "{{workflow.parameters.SLACK_CHANNEL_ID}}"
          - name: SLACK_TOKEN
            value: "slack-token"  # Replace with your actual secret name
          - name: SLACK_MESSAGE_TS
            value: "{{workflow.parameters.ENVIRONMENT_MESSAGE_TS}}"
          - name: SLACK_MESSAGE
            value: |
              ❌ **Environment Deployment Failed**
              
              **Environment:** {{workflow.parameters.ENVIRONMENT_NAME}}
              **Product:** {{workflow.parameters.PRODUCT_NAME}}
              **Release ID:** {{workflow.parameters.RELEASE_ID}}
              **Commit:** `{{workflow.parameters.COMMIT_SHA}}`
              **Error:** {{workflow.parameters.ERROR_MESSAGE}}
              
              Status: ❌ Failed
          - name: SLACK_TEMPLATE_BODY
            value: |
              {
                "color": "danger",
                "title": "❌ Environment Deployment Failed"
              }
          - name: SLACK_TEMPLATE_FIELDS
            value: |
              [
                {"title": "Environment", "value": "{{workflow.parameters.ENVIRONMENT_NAME}}", "short": true},
                {"title": "Product", "value": "{{workflow.parameters.PRODUCT_NAME}}", "short": true},
                {"title": "Release ID", "value": "{{workflow.parameters.RELEASE_ID}}", "short": true},
                {"title": "Commit", "value": "`{{workflow.parameters.COMMIT_SHA}}`", "short": true},
                {"title": "Status", "value": "❌ Failed", "short": true},
                {"title": "Error", "value": "{{workflow.parameters.ERROR_MESSAGE}}", "short": false}
              ]
